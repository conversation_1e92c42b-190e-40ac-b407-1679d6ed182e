-- SQL untuk menambahkan menu Penerimaan BLT ke sidebar
-- Jalankan query ini jika ingin menambahkan menu ke sidebar aplikasi

-- <PERSON><PERSON> apakah tabel menu ada
-- INSERT INTO msmenu (name, icon, url, parent_id, sort_order, is_active) 
-- VALUES ('Penerimaan BLT', 'fas fa-money-bill-wave', 'penerimaan_blt', NULL, 100, 1);

-- Atau jika menggunakan sistem submenu:
-- INSERT INTO mssubmenu (name, icon, url, menu_id, sort_order, is_active) 
-- VALUES ('Penerimaan BLT', 'fas fa-money-bill-wave', 'penerimaan_blt', [MENU_ID], 100, 1);

-- Catatan: 
-- 1. Sesuaikan nama tabel dan struktur dengan yang ada di aplikasi
-- 2. <PERSON>anti [MENU_ID] dengan ID menu parent yang sesuai
-- 3. Icon menggunakan Font Awesome: fas fa-money-bill-wave
-- 4. URL: penerimaan_blt (sesuai dengan routing yang sudah dibuat)
