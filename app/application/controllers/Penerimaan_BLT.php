<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Penerimaan_BLT_Model $penerimaan_blt
 * @property Detail_Penerima_BLT_Model $detail_penerima_blt
 * @property Warga_Model $warga
 * @property Datatables $datatables
 */
class Penerimaan_BLT extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Penerimaan_BLT_Model', 'penerimaan_blt');
        $this->load->model('Detail_Penerima_BLT_Model', 'detail_penerima_blt');
        $this->load->model('Warga_Model', 'warga');
        $this->load->library('datatables');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data['title'] = 'Penerimaan BLT';
        $data['content'] = 'penerimaan_blt/index';

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data['title'] = 'Tambah Penerimaan BLT';
        $data['content'] = 'penerimaan_blt/add';

        return $this->load->view('master', $data);
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->penerimaan_blt->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect('penerimaan_blt');
        }

        $data['title'] = 'Edit Penerimaan BLT';
        $data['content'] = 'penerimaan_blt/edit';
        $data['penerimaan_blt'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Access denied');
        }

        $datatables = $this->datatables->make('Penerimaan_BLT_Model', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        if (isAdmin()) {
            $where['a.id_user'] = getCurrentIdUser();
        }

        $data = array();
        foreach ($datatables->getData($where) as $value) {
            $data[] = array(
                $value->id,
                $value->deskripsi,
                number_format($value->nominal, 0, ',', '.'),
                $value->total_penerima ?? 0,
                $value->total_terverifikasi ?? 0,
                $value->total_menunggu ?? 0,
                date('d/m/Y H:i', strtotime($value->tanggal_dibuat)),
                '
                    <div class="btn-group" role="group">
                        <a href="' . base_url('penerimaan_blt/detail/' . $value->id) . '" class="btn btn-info btn-sm" title="Detail">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="' . base_url('penerimaan_blt/edit/' . $value->id) . '" class="btn btn-warning btn-sm" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button type="button" class="btn btn-danger btn-sm" title="Hapus" onclick="deleteData(' . $value->id . ')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                '
            );
        }

        return $datatables->json($data);
    }

    public function get_warga()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Access denied');
        }

        $warga = $this->warga->getDefaultData(array(
            'a.id_user' => getCurrentIdUser()
        ))->result();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'DATA' => $warga
        ));
    }

    public function process_add()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Access denied');
        }

        $deskripsi = getPost('deskripsi');
        $nominal = getPost('nominal');
        $warga_selected = getPost('warga_selected');

        if (empty($deskripsi)) {
            return JSONResponseDefault('FAILED', 'Deskripsi BLT harus diisi');
        }

        if (empty($nominal) || $nominal <= 0) {
            return JSONResponseDefault('FAILED', 'Nominal BLT harus diisi dan lebih dari 0');
        }

        if (empty($warga_selected) || !is_array($warga_selected)) {
            return JSONResponseDefault('FAILED', 'Pilih minimal satu warga penerima BLT');
        }

        // Insert data penerimaan BLT
        $insert_blt = array(
            'deskripsi' => $deskripsi,
            'nominal' => $nominal,
            'id_user' => getCurrentIdUser()
        );

        $this->db->trans_start();

        $insert_result = $this->penerimaan_blt->insert($insert_blt);
        if (!$insert_result) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', 'Gagal menyimpan data penerimaan BLT');
        }

        $id_penerimaan_blt = $this->db->insert_id();

        // Insert detail penerima BLT
        foreach ($warga_selected as $nik) {
            $insert_detail = array(
                'id_penerimaan_blt' => $id_penerimaan_blt,
                'nik' => $nik,
                'status_verifikasi' => 'menunggu_verifikasi'
            );

            $this->detail_penerima_blt->insert($insert_detail);
        }

        $this->db->trans_complete();

        if ($this->db->trans_status() === FALSE) {
            return JSONResponseDefault('FAILED', 'Gagal menyimpan data');
        }

        return JSONResponseDefault('OK', 'Data penerimaan BLT berhasil disimpan');
    }

    public function process_edit($id)
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Access denied');
        }

        $get = $this->penerimaan_blt->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $deskripsi = getPost('deskripsi');
        $nominal = getPost('nominal');
        $warga_selected = getPost('warga_selected');

        if (empty($deskripsi)) {
            return JSONResponseDefault('FAILED', 'Deskripsi BLT harus diisi');
        }

        if (empty($nominal) || $nominal <= 0) {
            return JSONResponseDefault('FAILED', 'Nominal BLT harus diisi dan lebih dari 0');
        }

        if (empty($warga_selected) || !is_array($warga_selected)) {
            return JSONResponseDefault('FAILED', 'Pilih minimal satu warga penerima BLT');
        }

        // Update data penerimaan BLT
        $update_blt = array(
            'deskripsi' => $deskripsi,
            'nominal' => $nominal
        );

        $this->db->trans_start();

        $update_result = $this->penerimaan_blt->update(array('id' => $id), $update_blt);
        if (!$update_result) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', 'Gagal mengupdate data penerimaan BLT');
        }

        // Hapus detail penerima lama
        $this->detail_penerima_blt->deleteByPenerimaanBLT($id);

        // Insert detail penerima BLT baru
        foreach ($warga_selected as $nik) {
            $insert_detail = array(
                'id_penerimaan_blt' => $id,
                'nik' => $nik,
                'status_verifikasi' => 'menunggu_verifikasi'
            );

            $this->detail_penerima_blt->insert($insert_detail);
        }

        $this->db->trans_complete();

        if ($this->db->trans_status() === FALSE) {
            return JSONResponseDefault('FAILED', 'Gagal mengupdate data');
        }

        return JSONResponseDefault('OK', 'Data penerimaan BLT berhasil diupdate');
    }

    public function process_delete()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Access denied');
        }

        $id = getPost('id');

        $get = $this->penerimaan_blt->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $this->db->trans_start();

        // Hapus detail penerima BLT
        $this->detail_penerima_blt->deleteByPenerimaanBLT($id);

        // Hapus data penerimaan BLT
        $delete_result = $this->penerimaan_blt->delete(array('id' => $id));

        $this->db->trans_complete();

        if ($this->db->trans_status() === FALSE || !$delete_result) {
            return JSONResponseDefault('FAILED', 'Gagal menghapus data');
        }

        return JSONResponseDefault('OK', 'Data penerimaan BLT berhasil dihapus');
    }

    public function detail($id)
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->penerimaan_blt->getDefaultData(array(
            'a.id' => $id,
            'a.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect('penerimaan_blt');
        }

        $data['title'] = 'Detail Penerimaan BLT';
        $data['content'] = 'penerimaan_blt/detail';
        $data['penerimaan_blt'] = $get->row();
        $data['detail_penerima'] = $this->penerimaan_blt->getDetailPenerima($id)->result();

        return $this->load->view('master', $data);
    }

    public function update_verifikasi()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Access denied');
        }

        $id_detail = getPost('id_detail');
        $status = getPost('status');

        if (!in_array($status, array('menunggu_verifikasi', 'terverifikasi'))) {
            return JSONResponseDefault('FAILED', 'Status verifikasi tidak valid');
        }

        $get = $this->detail_penerima_blt->getDataWithWarga(array(
            'a.id' => $id_detail,
            'c.id_user' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $update_result = $this->detail_penerima_blt->updateStatusVerifikasi($id_detail, $status);

        if ($update_result) {
            return JSONResponseDefault('OK', 'Status verifikasi berhasil diupdate');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal mengupdate status verifikasi');
        }
    }

    public function get_selected_warga($id)
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Access denied');
        }

        $selected_warga = $this->detail_penerima_blt->getDefaultData(array(
            'a.id_penerimaan_blt' => $id
        ))->result();

        $nik_array = array();
        foreach ($selected_warga as $warga) {
            $nik_array[] = $warga->nik;
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'DATA' => $nik_array
        ));
    }
}
