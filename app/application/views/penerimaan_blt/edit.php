<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Edit Penerimaan BLT</h4>
                    <div class="card-tools">
                        <a href="<?= base_url('penerimaan_blt') ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form id="form-edit-blt">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="deskripsi">Deskripsi BLT <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3" placeholder="Masukkan deskripsi BLT" required><?= $penerimaan_blt->deskripsi ?></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="nominal">Nominal BLT <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">Rp</span>
                                        </div>
                                        <input type="number" class="form-control" id="nominal" name="nominal" placeholder="0" min="1" value="<?= $penerimaan_blt->nominal ?>" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <label>Pilih Warga Penerima BLT <span class="text-danger">*</span></label>
                                    <div class="card">
                                        <div class="card-header">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input type="checkbox" class="form-check-input" id="select-all">
                                                        <label class="form-check-label" for="select-all">
                                                            <strong>Pilih Semua</strong>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <input type="text" class="form-control form-control-sm" id="search-warga" placeholder="Cari warga...">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                                            <div id="warga-list">
                                                <div class="text-center">
                                                    <div class="spinner-border" role="status">
                                                        <span class="sr-only">Loading...</span>
                                                    </div>
                                                    <p>Memuat data warga...</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Update
                                    </button>
                                    <a href="<?= base_url('penerimaan_blt') ?>" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> Batal
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var wargaData = [];
    var filteredWarga = [];
    var selectedWargaData = [];
    var penerimaanBltId = <?= $penerimaan_blt->id ?>;

    // Load warga data and selected warga
    loadWargaData();
    loadSelectedWarga();

    function loadWargaData() {
        $.ajax({
            url: '<?= base_url('penerimaan_blt/get_warga') ?>',
            type: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.RESULT === 'OK') {
                    wargaData = response.DATA;
                    filteredWarga = wargaData;
                    renderWargaList();
                } else {
                    $('#warga-list').html('<div class="alert alert-danger">Gagal memuat data warga</div>');
                }
            },
            error: function() {
                $('#warga-list').html('<div class="alert alert-danger">Terjadi kesalahan saat memuat data warga</div>');
            }
        });
    }

    function loadSelectedWarga() {
        $.ajax({
            url: '<?= base_url('penerimaan_blt/get_selected_warga/') ?>' + penerimaanBltId,
            type: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.RESULT === 'OK') {
                    selectedWargaData = response.DATA;
                    // Set checkboxes after warga list is rendered
                    setTimeout(function() {
                        setSelectedCheckboxes();
                    }, 500);
                }
            }
        });
    }

    function setSelectedCheckboxes() {
        selectedWargaData.forEach(function(nik) {
            $('#warga-' + nik).prop('checked', true);
        });
        updateSelectAllCheckbox();
    }

    function renderWargaList() {
        var html = '';
        if (filteredWarga.length === 0) {
            html = '<div class="alert alert-info">Tidak ada data warga yang ditemukan</div>';
        } else {
            filteredWarga.forEach(function(warga) {
                html += `
                    <div class="form-check mb-2">
                        <input type="checkbox" class="form-check-input warga-checkbox" id="warga-${warga.nik}" name="warga_selected[]" value="${warga.nik}">
                        <label class="form-check-label" for="warga-${warga.nik}">
                            <strong>${warga.nama}</strong> (${warga.nik})<br>
                            <small class="text-muted">${warga.alamat}, RT ${warga.rt}/RW ${warga.rw}</small>
                        </label>
                    </div>
                `;
            });
        }
        $('#warga-list').html(html);
        
        // Set selected checkboxes if data is already loaded
        if (selectedWargaData.length > 0) {
            setSelectedCheckboxes();
        }
    }

    function updateSelectAllCheckbox() {
        var totalCheckboxes = $('.warga-checkbox').length;
        var checkedCheckboxes = $('.warga-checkbox:checked').length;
        
        if (checkedCheckboxes === totalCheckboxes && totalCheckboxes > 0) {
            $('#select-all').prop('checked', true);
        } else {
            $('#select-all').prop('checked', false);
        }
    }

    // Search functionality
    $('#search-warga').on('keyup', function() {
        var searchTerm = $(this).val().toLowerCase();
        filteredWarga = wargaData.filter(function(warga) {
            return warga.nama.toLowerCase().includes(searchTerm) || 
                   warga.nik.includes(searchTerm) ||
                   warga.alamat.toLowerCase().includes(searchTerm);
        });
        renderWargaList();
        
        // Reset select all checkbox
        $('#select-all').prop('checked', false);
    });

    // Select all functionality
    $('#select-all').change(function() {
        var isChecked = $(this).is(':checked');
        $('.warga-checkbox').prop('checked', isChecked);
    });

    // Update select all when individual checkboxes change
    $(document).on('change', '.warga-checkbox', function() {
        updateSelectAllCheckbox();
    });

    // Form submission
    $('#form-edit-blt').submit(function(e) {
        e.preventDefault();
        
        var selectedWarga = [];
        $('.warga-checkbox:checked').each(function() {
            selectedWarga.push($(this).val());
        });

        if (selectedWarga.length === 0) {
            toastr.error('Pilih minimal satu warga penerima BLT');
            return;
        }

        var formData = {
            deskripsi: $('#deskripsi').val(),
            nominal: $('#nominal').val(),
            warga_selected: selectedWarga
        };

        $.ajax({
            url: '<?= base_url('penerimaan_blt/process_edit/' . $penerimaan_blt->id) ?>',
            type: 'POST',
            data: formData,
            dataType: 'json',
            beforeSend: function() {
                $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Mengupdate...');
            },
            success: function(response) {
                if (response.RESULT === 'OK') {
                    toastr.success(response.MESSAGE);
                    setTimeout(function() {
                        window.location.href = '<?= base_url('penerimaan_blt') ?>';
                    }, 1500);
                } else {
                    toastr.error(response.MESSAGE);
                    $('button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> Update');
                }
            },
            error: function() {
                toastr.error('Terjadi kesalahan saat mengupdate data');
                $('button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> Update');
            }
        });
    });

    // Format nominal input
    $('#nominal').on('input', function() {
        var value = $(this).val().replace(/[^0-9]/g, '');
        $(this).val(value);
    });
});
</script>

<style>
.card-tools {
    margin-left: auto;
}

.form-check-label {
    cursor: pointer;
}

.warga-checkbox {
    cursor: pointer;
}

#warga-list {
    max-height: 400px;
    overflow-y: auto;
}

.form-check {
    padding: 8px;
    border-bottom: 1px solid #eee;
}

.form-check:last-child {
    border-bottom: none;
}

.form-check:hover {
    background-color: #f8f9fa;
}
</style>
