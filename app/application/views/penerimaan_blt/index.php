<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Penerimaan BLT</h4>
                    <div class="card-tools">
                        <a href="<?= base_url('penerimaan_blt/add') ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Tambah Penerimaan BLT
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-penerimaan-blt" class="table table-striped table-bordered" style="width:100%">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Deskripsi</th>
                                    <th>Nominal</th>
                                    <th>Total Penerima</th>
                                    <th>Terverifikasi</th>
                                    <th>Menunggu</th>
                                    <th>Tanggal Dibuat</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Delete -->
<div class="modal fade" id="modal-delete" tabindex="-1" role="dialog" aria-labelledby="modal-delete-label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-delete-label">Konfirmasi Hapus</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus data penerimaan BLT ini?</p>
                <p class="text-danger"><strong>Perhatian:</strong> Data yang dihapus tidak dapat dikembalikan!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-danger" id="btn-confirm-delete">Hapus</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var table = $('#table-penerimaan-blt').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "<?= base_url('penerimaan_blt/datatables') ?>",
            "type": "POST"
        },
        "columns": [
            { "data": null, "orderable": false, "searchable": false },
            { "data": "deskripsi" },
            { 
                "data": "nominal",
                "render": function(data, type, row) {
                    return 'Rp ' + new Intl.NumberFormat('id-ID').format(data);
                }
            },
            { "data": "total_penerima" },
            { "data": "total_terverifikasi" },
            { "data": "total_menunggu" },
            { 
                "data": "tanggal_dibuat",
                "render": function(data, type, row) {
                    return moment(data).format('DD/MM/YYYY HH:mm');
                }
            },
            { 
                "data": null,
                "orderable": false,
                "searchable": false,
                "render": function(data, type, row) {
                    return `
                        <div class="btn-group" role="group">
                            <a href="<?= base_url('penerimaan_blt/detail/') ?>${row.id}" class="btn btn-info btn-sm" title="Detail">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="<?= base_url('penerimaan_blt/edit/') ?>${row.id}" class="btn btn-warning btn-sm" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button type="button" class="btn btn-danger btn-sm btn-delete" data-id="${row.id}" title="Hapus">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        "order": [[ 6, "desc" ]],
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });

    // Auto numbering
    table.on('order.dt search.dt', function () {
        table.column(0, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
            cell.innerHTML = i+1;
        } );
    } ).draw();

    // Delete functionality
    var deleteId = null;
    
    $(document).on('click', '.btn-delete', function() {
        deleteId = $(this).data('id');
        $('#modal-delete').modal('show');
    });

    $('#btn-confirm-delete').click(function() {
        if (deleteId) {
            $.ajax({
                url: '<?= base_url('penerimaan_blt/process_delete') ?>',
                type: 'POST',
                data: {
                    id: deleteId
                },
                dataType: 'json',
                success: function(response) {
                    $('#modal-delete').modal('hide');
                    if (response.RESULT === 'OK') {
                        toastr.success(response.MESSAGE);
                        table.ajax.reload();
                    } else {
                        toastr.error(response.MESSAGE);
                    }
                },
                error: function() {
                    $('#modal-delete').modal('hide');
                    toastr.error('Terjadi kesalahan saat menghapus data');
                }
            });
        }
    });
});
</script>

<style>
.card-tools {
    margin-left: auto;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}
</style>
