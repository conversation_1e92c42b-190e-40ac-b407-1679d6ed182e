<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h4 class="card-title">Penerimaan BLT</h4>
                    <div class="card-tools">
                        <a href="<?= base_url('penerimaan_blt/add') ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Tambah Penerimaan BLT
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-penerimaan-blt" class="table table-striped table-bordered" style="width:100%">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Deskripsi</th>
                                    <th>Nominal</th>
                                    <th>Total Penerima</th>
                                    <th>Terverifikasi</th>
                                    <th>Menunggu</th>
                                    <th>Tanggal Dibuat</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
    window.onload = function() {
        $('#table-penerimaan-blt').DataTable({
            ordering: false,
            processing: true,
            serverSide: true,
            ajax: {
                url: '<?= base_url(uri_string() . '/datatables') ?>',
                type: 'POST'
            },
        });
    };

    function deleteData(id) {
        swal({
            title: "Konfirmasi Hapus",
            text: "Apakah Anda yakin ingin menghapus data penerimaan BLT ini? Data yang dihapus tidak dapat dikembalikan!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Ya, Hapus!",
            cancelButtonText: "Batal",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: '<?= base_url('penerimaan_blt/process_delete') ?>',
                type: 'POST',
                data: {
                    id: id
                },
                dataType: 'json',
                success: function(response) {
                    if (response.RESULT === 'OK') {
                        swal("Berhasil!", response.MESSAGE, "success");
                        $('#table-penerimaan-blt').DataTable().ajax.reload();
                    } else {
                        swal("Gagal!", response.MESSAGE, "error");
                    }
                },
                error: function() {
                    swal("Error!", "Terjadi kesalahan saat menghapus data", "error");
                }
            });
        });
    }
</script>

<style>
    .card-tools {
        margin-left: auto;
    }

    .btn-group .btn {
        margin-right: 2px;
    }

    .btn-group .btn:last-child {
        margin-right: 0;
    }
</style>